package omnichannel_product_list

import (
	_ "embed"
	"log"
	"os"
	"testing"
	"time"
)

//go:embed product_list.json
var productList string

//go:embed product_info.json
var productInfo string

//go:embed product_attr.json
var productAttr string

//go:embed price.json
var priceInfo string

//go:embed price_type.json
var priceTypeInfo string

func TestGetChannelProductList(t *testing.T) {
	type args struct {
		productList []byte
		productInfo []byte
		productAttr []byte
	}
	tests := []struct {
		name    string
		args    args
		want    []byte
		wantErr bool
	}{
		{
			name: "case1",
			args: args{
				productList: []byte(productList),
				productInfo: []byte(productInfo),
				productAttr: []byte(productAttr),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			start := time.Now()
			t.Log("开始执行")
			list, err := GetChannelProductList(tt.args.productList, tt.args.productInfo, tt.args.productAttr, []byte(priceInfo), []byte(priceTypeInfo), "POS")
			if (err != nil) != tt.wantErr {
				t.Errorf("GetChannelProductList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			elapsed := time.Since(start)
			t.Log("执行时间: ", elapsed.Milliseconds())
			if err := os.WriteFile("file.json", list, 0666); err != nil {
				log.Fatal(err)
			}
			return
		})
	}
}

// 基准测试
func TestGetChannelProductList_B(t *testing.T) {

}
