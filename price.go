package omnichannel_product_list

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/buger/jsonparser"
	"github.com/spf13/cast"
	"golang.org/x/sync/errgroup"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"time"
)

// PriceQueryType 价格查询类型
type PriceQueryType int

const (
	// PriceQueryTypeCurrent 查询当前价格 modify信息不返回
	PriceQueryTypeCurrent PriceQueryType = 0
	// PriceQueryTypeModify 查询Modify 仅返回最外层Modify
	PriceQueryTypeModify PriceQueryType = 2
	// PriceQueryTypeAll 查询所有
	PriceQueryTypeAll PriceQueryType = 3
)

// PriceRequest 价格查询请求
type PriceRequest struct {
	ItemIDs                  []uint64       `json:"item_ids,omitempty"`
	StoreID                  uint64         `json:"store_id"`
	PriceTypeID              uint64         `json:"price_type_id,omitempty"`
	BatchID                  uint64         `json:"batch_id,omitempty"`
	QueryTime                *time.Time     `json:"query_time,omitempty"`
	QueryType                PriceQueryType `json:"query_type,omitempty"`
	ReturnExtendFields       string         `json:"return_extend_fields,omitempty"`
	ReturnModifyExtendFields string         `json:"return_modify_extend_fields,omitempty"`
	ItemCodes                []string       `json:"item_codes,omitempty"`
	Page                     *Page          `json:"page,omitempty"`
}

// Page 分页信息
type Page struct {
	Limit  int `json:"limit,omitempty"`
	Offset int `json:"offset,omitempty"`
}

// 默认重试配置
const (
	DefaultMaxRetries     = 3
	DefaultRetryDelay     = 500 * time.Millisecond
	DefaultExpireInterval = 7 * time.Hour * 24
)

type pullPriceOption struct {
	getFunc        func(string) ([]byte, error)
	saveFunc       func([]byte, string) error
	expireInterval time.Duration
	maxRetries     int
	retryDelay     time.Duration
}

type PullPriceOption func(*pullPriceOption)

func WithGetFunc(getFunc func() ([]byte, error)) PullPriceOption {
	return func(o *pullPriceOption) {
		o.getFunc = getFunc
	}
}

func WithSaveFunc(saveFunc func([]byte) error) PullPriceOption {
	return func(o *pullPriceOption) {
		o.saveFunc = saveFunc
	}
}

func WithExpireInterval(expireInterval time.Duration) PullPriceOption {
	return func(o *pullPriceOption) {
		o.expireInterval = expireInterval
	}
}

func WithMaxRetries(maxRetries int) PullPriceOption {
	return func(o *pullPriceOption) {
		o.maxRetries = maxRetries
	}
}

func WithRetryDelay(retryDelay time.Duration) PullPriceOption {
	return func(o *pullPriceOption) {
		o.retryDelay = retryDelay
	}
}

// sendPriceRequest 发送价格查询请求并处理重试逻辑
// url: 请求URL
// req: 请求体
// token: 认证令牌
// maxRetries: 最大重试次数
// retryDelay: 重试间隔时间
func sendPriceRequest(url string, req *PriceRequest, token string, maxRetries int, retryDelay time.Duration) ([]byte, error) {
	// 序列化请求体

	// 构建请求

	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	// 创建HTTP客户端
	client := &http.Client{}

	// 重试逻辑
	var respBody []byte
	var lastErr error

	for i := 0; i <= maxRetries; i++ {
		// 如果不是第一次尝试，等待一段时间再重试
		if i > 0 {
			time.Sleep(retryDelay)
		}
		respBody, err = getHttpResponse(http.MethodPost, url, token, reqBody, client)
		if err != nil {
			lastErr = err
			continue
		}
		return respBody, nil
	}

	// 所有重试都失败了，返回最后一个错误
	return nil, lastErr
}

// PullPriceInfo 从API拉取价格信息 有错误时nil
// host: API主机地址
// token: 认证令牌
// storeID: 店铺ID
// saveFunc: 保存价格信息的函数
// getFunc: 获取原有价格信息的函数

func PullPriceInfo(host string, token string, storeID uint64, lang string, options ...PullPriceOption) (*ProductListOpt, error) {
	var batchID uint64 = 0
	var currentInfo []byte
	opts := &pullPriceOption{
		maxRetries:     DefaultMaxRetries,
		retryDelay:     DefaultRetryDelay,
		expireInterval: DefaultExpireInterval,
		getFunc:        DefaultGetFunc("./"),
		saveFunc:       DefaultSaveFunc("./"),
	}
	for _, option := range options {
		option(opts)
	}

	// 构建保存路径
	currentInfo, err := opts.getFunc(DefaultPriceFileName)
	if err != nil {
		return nil, errors.Join(errors.New("读取原有价格信息失败: "), err)
	}

	// 获取批次ID和查询类型
	batchID, queryType, err := getBatchIdAndQueryType(currentInfo, opts.expireInterval)

	// 构建请求
	req := &PriceRequest{
		StoreID:   storeID,
		BatchID:   batchID,
		QueryType: queryType, // 获取所有价格信息
		Page: &Page{
			Limit: 0,
		},
	}

	// 发送请求
	url := fmt.Sprintf("%s%s", host, DefaultPriceApi)
	respBody, err := sendPriceRequest(url, req, token, opts.maxRetries, opts.retryDelay)
	if err != nil {
		return nil, err
	}

	// 使用errGroup
	priceInfo, err := BuildPriceInfoByResponse(respBody, currentInfo, queryType)
	if err != nil {
		return nil, err
	}
	ProductListOpts := &ProductListOpt{
		PriceInfo: priceInfo,
	}

	// todo  获取其他信息（product_info product_attr price_type product_list）返回map[string][]byte 文件名和内容

	// 保存到文件

	if err := opts.saveFunc(priceInfo, DefaultPriceFileName); err != nil {
		return nil, fmt.Errorf("保存价格信息失败: %w", err)
	}

	return ProductListOpts, nil
}

// GetPriceInfoFromFile 从文件中获取价格信息

func GetPriceInfoFromFile(queryTime int64, getFunc func() ([]byte, error), saveFunc func([]byte) error) ([]byte, error) {
	// 读取文件内容
	priceInfo, err := getFunc()
	if err != nil {
		return nil, fmt.Errorf("读取价格信息文件失败: %w", err)
	}

	// 处理当前生效的modify
	priceInfo, err = checkEffective(priceInfo, queryTime)

	// 保存
	if queryTime == 0 {
		if err := saveFunc(priceInfo); err != nil {
			return nil, fmt.Errorf("保存价格信息失败: %w", err)
		}
	}
	return priceInfo, nil
}

// BuildPriceInfoByResponse 根据API响应构建价格信息
// respBody: API响应体
// currentInfo: 当前价格信息（可能为nil）
// queryType: 查询类型
func BuildPriceInfoByResponse(respBody, currentInfo []byte, queryType PriceQueryType) ([]byte, error) {
	// 检查响应是否为空
	if len(respBody) == 0 {
		return nil, errors.New("响应体为空")
	}

	// 使用jsonparser解析payload部分
	payload, _, _, err := jsonparser.Get(respBody, "payload")
	if err != nil {
		return nil, fmt.Errorf("解析payload字段失败: %w", err)
	}
	if len(payload) == 0 {
		return nil, errors.New("响应中缺少payload字段")
	}

	// 获取批次ID
	batchID, err := jsonparser.GetString(payload, "batch_id")
	if err != nil {
		return nil, fmt.Errorf("获取batch_id失败: %w", err)
	}
	if batchID == "" {
		return nil, errors.New("响应中缺少batch_id字段")
	}

	// 获取拉取时间
	pullTime, err := jsonparser.GetString(payload, "pull_time")
	if err != nil {
		return nil, fmt.Errorf("获取pull_time失败: %w", err)
	}
	if pullTime == "" {
		return nil, errors.New("响应中缺少pull_time字段")
	}

	// 初始化结果
	result := currentInfo

	switch queryType {
	// 全量查询 直接覆盖
	case PriceQueryTypeAll, PriceQueryTypeCurrent:
		currentRows, _, _, err := jsonparser.Get(payload, "current", "items")
		if err != nil && !errors.Is(err, jsonparser.KeyPathNotFoundError) {
			log.Println("获取current.items失败: %w", err)
			return result, err
		}
		if len(result) == 0 {
			result = []byte(`{}`)
		}
		if len(currentRows) == 0 {
			currentRows = []byte(`[]`)
		}
		result, err = jsonparser.Set(result, currentRows, "rows")
		if err != nil {
			return nil, fmt.Errorf("设置rows字段失败: %w", err)
		}

		// 只查询modify 则合并
	case PriceQueryTypeModify:
		// 查询修改价格，提取 modify 部分
		modify, _, _, err := jsonparser.Get(payload, "modify", "items")
		if err != nil && !errors.Is(err, jsonparser.KeyPathNotFoundError) {
			log.Println("获取modify字段失败: %w", err)
			return result, err
		}

		// 如果有modify，则合并当前信息和新信息
		result, err = mergeModifyIntoFile(result, modify)
		if err != nil {
			return nil, fmt.Errorf("合并修改信息失败: %w", err)
		}
		// 增量查询时 需要检查modify 元素是否已生效，如果已生效则更新rows，并删除modify中的元素
		result, err = checkEffective(result, 0)
		if err != nil {
			return nil, fmt.Errorf("检查价格生效状态失败: %w", err)
		}

	default:
		return nil, errors.New("不支持的查询类型")
	}

	// 设置批次ID和拉取时间
	result, err = jsonparser.Set(result, []byte(fmt.Sprintf(`"%s"`, batchID)), "batch_id")
	if err != nil {
		return nil, fmt.Errorf("设置batch_id字段失败: %w", err)
	}

	result, err = jsonparser.Set(result, []byte(fmt.Sprintf(`"%s"`, pullTime)), "pull_time")

	if err != nil {
		return nil, fmt.Errorf("设置pull_time字段失败: %w", err)
	}
	return result, nil
}

// mergeModifyIntoFile 合并当前信息和修改信息
func mergeModifyIntoFile(fileBs, modifyItems []byte) ([]byte, error) {
	// 检查输入
	if len(modifyItems) == 0 {
		return fileBs, nil
	}

	// 获取当前信息的项目
	var currentRows []byte
	var err error

	// 先尝试从current中获取items
	currentRows, _, _, err = jsonparser.Get(fileBs, "rows")
	if err != nil {
		if err != jsonparser.KeyPathNotFoundError {
			log.Println("获取current.items失败: %w", err)
			return fileBs, nil
		}
	}
	// 合并项目
	rowsBytes, err := mergeItems(currentRows, modifyItems)
	if err != nil {
		return nil, fmt.Errorf("合并项目失败: %w", err)
	}

	// 更新当前文件的rows
	fileBs, err = jsonparser.Set(fileBs, rowsBytes, "rows")
	return fileBs, err
}

// mergeItems 合并两个项目数组，以item_id+price_type_id为键
// 如果修改项目中有相同的item_id，则替换当前项目中的相应项目
func mergeItems(currentItems, modifyItems []byte) ([]byte, error) {

	if len(modifyItems) == 0 {
		return currentItems, nil
	}

	// 将当前项目转换为map，以item_id为键
	currentItemsMap := make(map[string][]byte)

	jsonparser.ArrayEach(currentItems, func(value []byte, dataType jsonparser.ValueType, offset int, err error) {
		itemID, err := jsonparser.GetString(value, "item_id")
		if err != nil {
			return
		}
		priceTypeID, err := jsonparser.GetString(value, "price_type_id")
		if err != nil {
			return
		}
		key := fmt.Sprintf("%s-%s", itemID, priceTypeID)
		currentItemsMap[key] = value
	})

	// 处理修改项目，更新或添加到map中
	var psrserErr error
	jsonparser.ArrayEach(modifyItems, func(modifyItem []byte, dataType jsonparser.ValueType, offset int, err error) {
		itemID, err := jsonparser.GetString(modifyItem, "item_id")
		if err != nil {
			psrserErr = err
			return
		}
		priceTypeID, err := jsonparser.GetString(modifyItem, "price_type_id")
		if err != nil {
			psrserErr = err
			return
		}
		key := fmt.Sprintf("%s-%s", itemID, priceTypeID)
		modify, _, _, err := jsonparser.Get(modifyItem, "modify")
		if err != nil {
			psrserErr = err
			return
		}

		// 检查当前项目中是否已存在该item_id-price_type_id
		currentItem, exists := currentItemsMap[key]
		if exists {
			// 如果存在，将修改项目的modify字段合并到当前项目的modify后面
			currentModify, _, _, err := jsonparser.Get(currentItem, "modify")
			if err != nil && err != jsonparser.KeyPathNotFoundError {
				psrserErr = err
				return
			}

			// 如果当前项目没有modify字段，则创建一个空数组
			if err == jsonparser.KeyPathNotFoundError || len(currentModify) == 0 {
				currentModify = []byte("[]")
			}

			// 将修改项目的modify字段解析为数组
			var modifyArray []json.RawMessage
			if err := json.Unmarshal(modify, &modifyArray); err != nil {
				psrserErr = err
				return
			}

			// 将当前项目的modify字段解析为数组
			var currentModifyArray []json.RawMessage
			if err := json.Unmarshal(currentModify, &currentModifyArray); err != nil {
				psrserErr = err
				return
			}

			// 合并两个数组
			mergedModifyArray := append(currentModifyArray, modifyArray...)

			// 将合并后的数组序列化为JSON
			mergedModify, err := json.Marshal(mergedModifyArray)
			if err != nil {
				psrserErr = err
				return
			}

			// 更新当前项目的modify字段
			updatedItem, err := jsonparser.Set(currentItem, mergedModify, "modify")
			if err != nil {
				psrserErr = err
				return
			}

			// 更新map中的项目
			currentItemsMap[key] = updatedItem
			return
		}
		if !exists {
			// 如果不存在，直接添加修改项目
			modifyItem, err := jsonparser.Set(modifyItem, []byte("false"), "is_valid")
			if err != nil {
				psrserErr = err
				return
			}
			currentItemsMap[key] = modifyItem
			return
		}

	})

	// 检查解析过程中是否有错误
	if psrserErr != nil {
		return nil, fmt.Errorf("处理修改项目时出错: %w", psrserErr)
	}

	// 将map转换回数组
	var result []json.RawMessage
	for _, item := range currentItemsMap {
		result = append(result, json.RawMessage(item))
	}

	// 序列化结果
	mergedItems, err := json.Marshal(result)
	if err != nil {
		return nil, fmt.Errorf("序列化合并项目失败: %w", err)
	}

	return mergedItems, nil
}

func getBatchIdAndQueryType(content []byte, expireInterval time.Duration) (batchId uint64, queryType PriceQueryType, err error) {

	//默认拉取全量
	queryType = PriceQueryTypeAll
	if len(content) > 0 {
		batchIDStr, _ := jsonparser.GetString(content, "batch_id")
		batchId = cast.ToUint64(batchIDStr)

		pullTimeStr, _ := jsonparser.GetString(content, "pull_time")

		var pullTime time.Time
		pullTime, err = time.Parse(time.RFC3339, pullTimeStr)
		if err != nil {
			log.Println("解析pull_time失败: %w", err)
			return
		}
		// 如果距离上次拉取时间超过3天，则重新拉取完整价格信息
		if time.Since(pullTime) > expireInterval {
			batchId = 0
		}
	}
	if batchId != 0 {
		queryType = PriceQueryTypeModify
	}
	return
}

// 增量查询时 需要检查modify 元素是否已生效，如果已生效则更新rows，并删除modify中的元素
func checkEffective(fileBs []byte, queryTime int64) ([]byte, error) {
	if len(fileBs) == 0 {
		return fileBs, nil
	}

	// 解析JSON
	rows, _, _, err := jsonparser.Get(fileBs, "rows")
	if err != nil {
		return fileBs, fmt.Errorf("获取rows字段失败: %w", err)
	}

	// 将rows解析为数组
	var rowsArray []json.RawMessage
	if err := json.Unmarshal(rows, &rowsArray); err != nil {
		return fileBs, fmt.Errorf("解析rows字段失败: %w", err)
	}

	// 当前时间
	if queryTime == 0 {
		queryTime = time.Now().Unix()
	}

	qt := time.Unix(queryTime, 0)

	// 遍历所有项目，检查modify元素是否已生效
	for i, item := range rowsArray {
		// 获取modify字段
		modify, _, _, err := jsonparser.Get(item, "modify")
		if err != nil {
			if err == jsonparser.KeyPathNotFoundError {
				continue // 没有modify字段，跳过
			}
			return fileBs, fmt.Errorf("获取modify字段失败: %w", err)
		}

		// 将modify解析为数组
		var modifyArray []json.RawMessage
		if err := json.Unmarshal(modify, &modifyArray); err != nil {
			return fileBs, fmt.Errorf("解析modify字段失败: %w", err)
		}

		// 如果没有modify元素，跳过
		if len(modifyArray) == 0 {
			continue
		}

		// 检查每个modify元素是否已生效
		var effectiveModifie json.RawMessage
		// 最近生效的时间
		var latestEffectiveTime time.Time
		var remainingModifies []json.RawMessage
		var hasEffective bool

		for _, modifyItem := range modifyArray {
			// 获取effective_time字段
			effectiveTimeStr, err := jsonparser.GetString(modifyItem, "effective_time")
			if err != nil {
				if err == jsonparser.KeyPathNotFoundError {
					// 没有effective_time字段，保留该modify元素
					remainingModifies = append(remainingModifies, modifyItem)
					continue
				}
				return fileBs, fmt.Errorf("获取effective_time字段失败: %w", err)
			}

			// 解析effective_time
			effectiveTime, err := time.Parse(time.RFC3339, effectiveTimeStr)
			if err != nil {
				return fileBs, fmt.Errorf("解析effective_time字段失败: %w", err)
			}

			// 检查是否已生效
			if qt.After(effectiveTime) || qt.Equal(effectiveTime) {
				// 已生效，添加到effectiveModifies
				if effectiveTime.After(latestEffectiveTime) {
					effectiveModifie = modifyItem
					latestEffectiveTime = effectiveTime
				}

				hasEffective = true

			} else {
				// 未生效，保留该modify元素
				remainingModifies = append(remainingModifies, modifyItem)
			}
		}

		// 如果有已生效的modify元素，更新当前项目
		if hasEffective {
			item = effectiveModifie
			// 移除生效时间字段
			item = jsonparser.Delete(item, "effective_time")

			// 更新is_valid字段
			item, err = jsonparser.Set(item, []byte("true"), "is_valid")
			if err != nil {
				return fileBs, fmt.Errorf("更新is_valid字段失败: %w", err)
			}

			// 更新modify字段，只保留未生效的modify元素
			remainingModifyBytes, err := json.Marshal(remainingModifies)
			if err != nil {
				return fileBs, fmt.Errorf("序列化remainingModifies失败: %w", err)
			}

			item, err = jsonparser.Set(item, remainingModifyBytes, "modify")
			if err != nil {
				return fileBs, fmt.Errorf("更新modify字段失败: %w", err)
			}

			// 更新rowsArray中的项目
			rowsArray[i] = json.RawMessage(item)
		}
	}

	// 序列化更新后的rowsArray
	updatedRows, err := json.Marshal(rowsArray)
	if err != nil {
		return fileBs, fmt.Errorf("序列化更新后的rows失败: %w", err)
	}

	// 更新fileBs中的rows字段
	result, err := jsonparser.Set(fileBs, updatedRows, "rows")
	if err != nil {
		return fileBs, fmt.Errorf("更新rows字段失败: %w", err)
	}

	return result, nil
}

func getFilePath(dir string, fileName string) string {
	return filepath.Join(dir, fileName)
}

func DefaultSaveFunc(dir string) func([]byte, string) error {
	return func(priceInfo []byte, fileName string) error {
		savePath := getFilePath(dir, fileName)
		if savePath == "" {
			return errors.New("保存路径不能为空")
		}
		dir := filepath.Dir(savePath)
		if dir != "" && dir != "." { // 只为子目录创建文件夹
			if err := os.MkdirAll(dir, 0755); err != nil {
				return fmt.Errorf("创建目录失败: %w", err)
			}
		}
		// 总是尝试写入文件
		if err := os.WriteFile(savePath, priceInfo, 0644); err != nil {
			return fmt.Errorf("写入文件失败: %w", err)
		}
		return nil
	}
}

func DefaultGetFunc(dir string) func(string) ([]byte, error) {
	return func(fileName string) ([]byte, error) {
		savePath := getFilePath(dir, fileName)
		if _, err := os.Stat(savePath); os.IsNotExist(err) {
			return nil, nil
		}
		// get 从文件中读取价格信息
		if savePath == "" {
			return nil, errors.New("保存路径不能为空")
		}
		// 读取文件
		return os.ReadFile(savePath)
	}
}

func getHttpResponse(method, url string, token string, reqBody []byte, client *http.Client) ([]byte, error) {
	// 创建HTTP请求
	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")
	if token != "" {
		httpReq.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))
	}

	// 发送请求
	resp, err := client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %w", err)
	}

	// 检查状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API返回错误状态码: %d", resp.StatusCode)
	}

	// 读取响应体
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}
	defer resp.Body.Close()
	// 判断status_code
	statusCode, err := jsonparser.GetInt(respBody, "status_code")
	if err != nil {
		return nil, fmt.Errorf("读取响应状态码失败: %w", err)

	}
	if statusCode != 0 {
		return nil, fmt.Errorf("API返回错误状态码: %d,resp:%s", statusCode, respBody)

	}
	// 请求成功，跳出循环
	return respBody, nil
}

func GetMenuData(host string, token string, storeID uint64, lang string, channelCode string, options ...PullPriceOption) ([]byte, error) {
	opts := &pullPriceOption{
		maxRetries:     DefaultMaxRetries,
		retryDelay:     DefaultRetryDelay,
		expireInterval: DefaultExpireInterval,
		getFunc:        DefaultGetFunc("./"),
		saveFunc:       DefaultSaveFunc("./"),
	}
	for _, option := range options {
		option(opts)
	}
	menuOpts := &ProductListOpt{}
	var err error
	// 先抓取线上数据
	menuOpts, err = PullPriceInfo(host, token, storeID, lang, options...)
	if err != nil {
		log.Printf("拉取云端数据失败：%v\n使用本地缓存数据", err)

		// 使用errgroup 并发读取本地数据
		g, ctx := errgroup.WithContext(ctx)

		menuOpts.PriceInfo, err = opts.getFunc(DefaultPriceFileName)
		if err != nil {
			return nil, fmt.Errorf("读取price本地数据失败：%v\n", err)
		}
		menuOpts.PriceTypeInfo, err = opts.getFunc(DefaultPriceTypeFileName)
		if err != nil {
			return nil, fmt.Errorf("读取price_type本地数据失败：%v\n", err)
		}
		menuOpts.ProductInfo, err = opts.getFunc(DefaultProductInfoFileName)
		if err != nil {
			return nil, fmt.Errorf("读取product_info本地数据失败：%v\n", err)
		}
		menuOpts.ProductAttr, err = opts.getFunc(DefaultProductAttrFileName)
		if err != nil {
			return nil, fmt.Errorf("读取product_attr本地数据失败：%v\n", err)
		}
		menuOpts.ProductList, err = opts.getFunc(DefaultProductListFileName)
		if err != nil {
			return nil, fmt.Errorf("读取product_list本地数据失败：%v\n", err)
		}
	}
	return GetChannelProductList(channelCode, menuOpts)
}
