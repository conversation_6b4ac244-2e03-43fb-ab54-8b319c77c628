package omnichannel_product_list

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/buger/jsonparser"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"time"
)

// PriceQueryType 价格查询类型
type PriceQueryType int

const (
	// PriceQueryTypeCurrent 查询当前价格 modify信息不返回
	PriceQueryTypeCurrent PriceQueryType = 0
	// PriceQueryTypeModify 查询Modify 仅返回最外层Modify
	PriceQueryTypeModify PriceQueryType = 2
	// PriceQueryTypeAll 查询所有
	PriceQueryTypeAll PriceQueryType = 3
)

// PriceRequest 价格查询请求
type PriceRequest struct {
	ItemIDs                  []uint64       `json:"item_ids,omitempty"`
	StoreID                  uint64         `json:"store_id"`
	PriceTypeID              uint64         `json:"price_type_id,omitempty"`
	BatchID                  uint64         `json:"batch_id,omitempty"`
	QueryTime                *time.Time     `json:"query_time,omitempty"`
	QueryType                PriceQueryType `json:"query_type,omitempty"`
	ReturnExtendFields       string         `json:"return_extend_fields,omitempty"`
	ReturnModifyExtendFields string         `json:"return_modify_extend_fields,omitempty"`
	ItemCodes                []string       `json:"item_codes,omitempty"`
	Page                     *Page          `json:"page,omitempty"`
}

// Page 分页信息
type Page struct {
	Limit  int `json:"limit,omitempty"`
	Offset int `json:"offset,omitempty"`
}

// PullPriceInfo 从API拉取价格信息
// host: API主机地址
// token: 认证令牌
// storeID: 店铺ID
// priceTypeID: 价格类型ID
// savePath: 保存路径
func PullPriceInfo(host string, token string, storeID uint64, priceTypeID uint64, savePath string) ([]byte, error) {
	var batchID uint64 = 0
	var currentInfo []byte

	// 检查文件是否存在
	if _, err := os.Stat(savePath); err == nil {
		// 文件存在，读取文件内容用于增量更新
		currentInfo, err = os.ReadFile(savePath)
		if err != nil {
			return nil, fmt.Errorf("读取价格信息文件失败: %w", err)
		}
	}

	// 获取批次ID和查询类型
	batchID, queryType, err := getBatchIdAndQueryType(currentInfo)

	// 构建请求
	req := PriceRequest{
		StoreID:   storeID,
		BatchID:   batchID,
		QueryType: queryType, // 获取所有价格信息
		Page: &Page{
			Limit: -1,
		},
	}

	// 发送请求
	url := fmt.Sprintf("%s%s", host, DefaultHost)
	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}
	httpReq.Header.Set("Content-Type", "application/json")
	if token != "" {
		httpReq.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))
	}

	client := &http.Client{}
	resp, err := client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API返回错误状态码: %d", resp.StatusCode)
	}

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 保存到文件
	if savePath != "" {
		if err := Save(respBody, savePath); err != nil {
			return nil, fmt.Errorf("保存价格信息失败: %w", err)
		}
	}

	return respBody, nil
}

// Save 保存价格信息到文件
func Save(priceInfo []byte, savePath string) error {
	if savePath == "" {
		return errors.New("保存路径不能为空")
	}

	// 确保目录存在
	dir := filepath.Dir(savePath)
	if dir != "" && dir != "." {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("创建目录失败: %w", err)
		}
	}

	// 写入文件
	if err := os.WriteFile(savePath, priceInfo, 0644); err != nil {
		return fmt.Errorf("写入文件失败: %w", err)
	}

	return nil
}

// GetPriceInfoFromFile 从文件中获取价格信息
func GetPriceInfoFromFile(filePath string) ([]byte, error) {
	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil, fmt.Errorf("价格信息文件不存在: %w", err)
	}

	// 读取文件内容
	priceInfo, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取价格信息文件失败: %w", err)
	}

	return GetPriceInfo(priceInfo)
}

// GetPriceInfo 获取当前可用的价格信息
func GetPriceInfo(priceInfo []byte) ([]byte, error) {
	if len(priceInfo) == 0 {
		return nil, errors.New("价格信息为空")
	}

	// 检查批次ID
	batchID := gjson.GetBytes(priceInfo, "batch_id").Uint()
	if batchID == 0 {
		return nil, errors.New("无效的批次ID，需要重新拉取完整价格信息")
	}

	// 检查是否有current字段
	if !gjson.GetBytes(priceInfo, "current").Exists() && !gjson.GetBytes(priceInfo, "modify").Exists() {
		return nil, errors.New("价格信息格式错误，缺少current或modify字段")
	}

	// 获取当前价格信息
	var result []byte
	var err error

	// 如果有current字段，直接返回
	if gjson.GetBytes(priceInfo, "current").Exists() {
		current := gjson.GetBytes(priceInfo, "current")
		result, err = sjson.SetBytes([]byte{}, "items", current.Get("items").Value())
		if err != nil {
			return nil, fmt.Errorf("处理current字段失败: %w", err)
		}
	} else {
		// 否则处理modify字段
		modify := gjson.GetBytes(priceInfo, "modify")
		result, err = sjson.SetBytes([]byte{}, "items", modify.Get("items").Value())
		if err != nil {
			return nil, fmt.Errorf("处理modify字段失败: %w", err)
		}
	}

	// 添加批次ID和拉取时间
	result, err = sjson.SetBytes(result, "batch_id", batchID)
	if err != nil {
		return nil, fmt.Errorf("添加批次ID失败: %w", err)
	}

	pullTime := gjson.GetBytes(priceInfo, "pull_time").String()
	result, err = sjson.SetBytes(result, "pull_time", pullTime)
	if err != nil {
		return nil, fmt.Errorf("添加拉取时间失败: %w", err)
	}

	return result, nil
}

func getBatchIdAndQueryType(content []byte) (uint64, PriceQueryType, error) {
	var batchID int64
	//默认拉取全量
	queryType := PriceQueryTypeAll
	if len(content) > 0 {
		batchID, _ = jsonparser.GetInt(content, "batch_id")

		pullTimeStr, err := jsonparser.GetString(content, "pull_time")
		if err != nil {
			return 0, 0, fmt.Errorf("读取价格信息文件pull_time失败: %w", err)
		}

		pullTime, err := time.Parse(time.RFC3339, pullTimeStr)
		if err != nil {
			return 0, 0, fmt.Errorf("解析价格信息文件pull_time失败: %w", err)
		}

		// 如果距离上次拉取时间超过3天，则重新拉取完整价格信息
		if time.Since(pullTime) > time.Hour*24*3 {
			batchID = 0
		}
	}
	if batchID != 0 {
		queryType = PriceQueryTypeModify
	}
	return uint64(batchID), queryType, nil
}
