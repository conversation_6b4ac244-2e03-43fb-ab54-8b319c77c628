package omnichannel_product_list

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"path/filepath"
	"testing"
	"time"
)

func TestPullPriceInfo(t *testing.T) {
	// 创建模拟服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 检查请求方法和路径
		if r.Method != "POST" || r.URL.Path != "/api/v1/price-center/get-product-price-full-info-agent" {
			t.Erro<PERSON>("预期请求 POST /api/v1/price-center/get-product-price-full-info-agent, 实际收到 %s %s", r.Method, r.URL.Path)
			w.<PERSON><PERSON>ead<PERSON>(http.StatusNotFound)
			return
		}

		// 解析请求体
		var req PriceRequest
		decoder := json.NewDecoder(r.Body)
		if err := decoder.Decode(&req); err != nil {
			t.<PERSON>("解析请求体失败: %v", err)
			w.<PERSON><PERSON>(http.StatusBadRequest)
			return
		}

		// 构造响应
		response := map[string]interface{}{
			"current": map[string]interface{}{
				"items": []map[string]interface{}{
					{
						"item_id":       uint64(1001),
						"item_code":     "ITEM001",
						"item_name":     "测试商品1",
						"price_type_id": uint64(1),
						"price":         "10.00",
						"tax_rate":      "0.06",
						"use_date":      "2023-01-01",
						"hidden":        false,
						"takeout_price": "12.00",
						"is_valid":      true,
					},
				},
			},
			"batch_id": uint64(12345),
			"total":    int64(1),
		}

		// 返回响应
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()

	// 创建临时文件
	tempDir, err := os.MkdirTemp("", "price-test")
	if err != nil {
		t.Fatalf("创建临时目录失败: %v", err)
	}
	defer os.RemoveAll(tempDir)

	savePath := filepath.Join(tempDir, "price.json")

	// 测试拉取价格信息
	result, err := PullPriceInfo(server.URL, "", 1, 1, savePath)
	if err != nil {
		t.Fatalf("拉取价格信息失败: %v", err)
	}

	// 验证结果
	if result == nil {
		t.Fatal("拉取结果为空")
	}

	// 验证文件是否已保存
	if _, err := os.Stat(savePath); os.IsNotExist(err) {
		t.Fatalf("价格信息未保存到文件: %v", err)
	}

	// 测试增量更新 - 由于函数签名变更，这里不再直接传递result
	// 第二次调用会自动从savePath读取文件进行增量更新
	result2, err := PullPriceInfo(server.URL, "", 1, 1, savePath)
	if err != nil {
		t.Fatalf("增量更新失败: %v", err)
	}

	if result2 == nil {
		t.Fatal("增量更新结果为空")
	}
}

func TestGetPriceInfo(t *testing.T) {
	// 准备测试数据
	testData := map[string]interface{}{
		"current": map[string]interface{}{
			"items": []map[string]interface{}{
				{
					"item_id":       uint64(1001),
					"item_code":     "ITEM001",
					"item_name":     "测试商品1",
					"price_type_id": uint64(1),
					"price":         "10.00",
					"is_valid":      true,
				},
			},
		},
		"batch_id":  uint64(12345),
		"pull_time": time.Now().Format(time.RFC3339),
	}

	// 序列化测试数据
	testDataBytes, err := json.Marshal(testData)
	if err != nil {
		t.Fatalf("序列化测试数据失败: %v", err)
	}

	// 测试获取价格信息
	result, err := GetPriceInfo(testDataBytes)
	if err != nil {
		t.Fatalf("获取价格信息失败: %v", err)
	}

	// 验证结果
	if result == nil {
		t.Fatal("获取结果为空")
	}

	// 测试空数据
	_, err = GetPriceInfo(nil)
	if err == nil {
		t.Fatal("预期空数据应返回错误，但未返回")
	}

	// 测试无效批次ID
	invalidData := map[string]interface{}{
		"current": map[string]interface{}{
			"items": []map[string]interface{}{},
		},
		"batch_id":  uint64(0),
		"pull_time": time.Now().Format(time.RFC3339),
	}
	invalidDataBytes, _ := json.Marshal(invalidData)
	_, err = GetPriceInfo(invalidDataBytes)
	if err == nil {
		t.Fatal("预期无效批次ID应返回错误，但未返回")
	}
}

func TestSave(t *testing.T) {
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "price-save-test")
	if err != nil {
		t.Fatalf("创建临时目录失败: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 准备测试数据
	testData := map[string]interface{}{
		"test": "data",
	}
	testDataBytes, _ := json.Marshal(testData)

	// 测试保存到文件
	savePath := filepath.Join(tempDir, "subdir", "price.json")
	err = Save(testDataBytes, savePath)
	if err != nil {
		t.Fatalf("保存价格信息失败: %v", err)
	}

	// 验证文件是否已保存
	if _, err := os.Stat(savePath); os.IsNotExist(err) {
		t.Fatalf("价格信息未保存到文件: %v", err)
	}

	// 读取文件内容并验证
	content, err := os.ReadFile(savePath)
	if err != nil {
		t.Fatalf("读取保存的文件失败: %v", err)
	}

	if string(content) != string(testDataBytes) {
		t.Fatalf("保存的内容与原始数据不匹配")
	}

	// 测试空路径
	err = Save(testDataBytes, "")
	if err == nil {
		t.Fatal("预期空路径应返回错误，但未返回")
	}
}
