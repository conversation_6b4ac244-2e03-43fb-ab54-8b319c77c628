package omnichannel_product_list

import (
	"bytes"
	"encoding/json"
	"errors"
	"github.com/buger/jsonparser"
)

// GetChannelProductList 拼接菜单数据
// 根据下面这3个接口数据组装为老的菜单接口数据:/api/omnichannel/channel/product/list
// 1、属性列表接口: /api/omnichannel/channel/product/attr/list
// 2、商品接口: /api/omnichannel/channel/product/productInfo/list
// 3、菜单关系接口: /api/omnichannel/channel/product/list/v2
//
//	productList-->>product_id(根据商品id,在商品列表接口中"product_id"找到对应商品,补充商品信息))
//
//	productList-->>relation-->>additional_attribute-->>id(根据属性组id,在属性列表接口中"id"找到对应属性组,补充属性信息))
//	productList-->>relation-->>additional_attribute-->>attribute_values-->>id(属性列表接口中先根据属性组id,再根据属性值"attribute_values"-->>"id"找到对应属性组中的属性值,补充属性值信息))
//
//	productList-->>relation-->>set-->>spu_id (根据商品spu_id,在商品列表接口中"product_id"找到对应商品,补充商品信息), 放在set中新增字段:"subItemSpu":{},把商品信息放入其中.
//
//	productList-->>relation-->>set-->>addition(加料如果不为空,把addition 移动替换掉 subItemSpu-->>relation-->>addition),把addition删除
//	productList-->>relation-->>set-->>raisePrice 赋值给: subItemSpu-->>relation-->>sku-->>selling_price;
//	productList-->>relation-->>set-->>subItemSpu-->>relation-->>sku-->>sellingPrice 赋值给: subItemSpu-->>relation-->>sku-->>original_price;
//
//	productList-->>relation-->>addition-->>新增: additional_attribute (根据商品spu_id,在商品列表接口中"product_id"找到对应商品->>relation-->>additional_attribute),并补充属性信息

type ProductListOpt struct {
	ProductList   []byte
	ProductInfo   []byte
	ProductAttr   []byte
	PriceInfo     []byte
	PriceTypeInfo []byte
}

func GetChannelProductList(channelCode string, opts *ProductListOpt) ([]byte, error) {
	var productListArray []map[string]interface{}
	decoder := json.NewDecoder(bytes.NewReader(opts.ProductList))
	decoder.UseNumber()

	if err := decoder.Decode(&productListArray); err != nil {
		return nil, errors.New("unmarshal product info error: " + err.Error())
	}

	productInfoMap, err := getProductInfoMap(opts.ProductInfo)
	if err != nil {
		return nil, errors.New("get product info map error: " + err.Error())
	}

	productAttrMap, err := getProductAttr(opts.ProductAttr)
	if err != nil {
		return nil, errors.New("get product attr map error: " + err.Error())
	}

	productAttrValuesMap, err := getProductAttrValues(opts.ProductAttr)
	if err != nil {
		return nil, errors.New("get product attr values map error: " + err.Error())
	}

	priceInfoMap, err := getPriceInfoMap(opts.PriceInfo)
	if err != nil {
		return nil, errors.New("get price info map error: " + err.Error())
	}

	priceTypeMap, channelMap, err := getPriceTypeMapAndChannelMap(opts.PriceTypeInfo)
	if err != nil {
		return nil, errors.New("get price type info and channel map error: " + err.Error())
	}
	filterProductListArray := make([]map[string]interface{}, 0, len(productListArray))
	for _, category := range productListArray {
		productList, ok := category["productList"].([]interface{})
		if !ok {
			return nil, errors.New("productList is not an array")
		}
		filterProductList := make([]interface{}, 0, len(productList))
		for _, product := range productList {
			productMap, ok := product.(map[string]interface{})
			if !ok {
				return nil, errors.New("product is not a map")
			}

			// 设置商品信息
			if err := setProductInfo(productMap, productInfoMap); err != nil {
				return nil, errors.Join(errors.New("set product info error: "), err)
			}

			// 设置商品属性
			if err := setProductAttr(productMap, productAttrMap); err != nil {
				return nil, err
			}

			// 补充attribute_values
			if err := setAttrValues(productMap, productAttrValuesMap); err != nil {
				return nil, errors.Join(errors.New("set attr values error: "), err)
			}

			// 补充subItemSpu
			// productList-->>relation-->>set-->>spu_id (根据商品spu_id,在商品列表接口中"product_id"找到对应商品,补充商品信息),
			// 放在set中新增字段:"subItemSpu":{},把商品信息放入其中.
			if err := setSubItemSpu(productMap, productInfoMap, productAttrMap, productAttrValuesMap); err != nil {
				return nil, errors.Join(errors.New("set sub item spu error: "), err)
			}

			// 设置relation addition
			// productList-->>relation-->>set-->>addition(加料如果不为空,把addition 移动替换掉 subItemSpu-->>relation-->>addition),把addition删除
			if err := setRelationAddition(productMap, productInfoMap); err != nil {
				return nil, errors.Join(errors.New("set relation addition error: "), err)
			}

			// 过滤并填充价格信息
			priceTypeID, ok := channelMap[channelCode]
			if !ok {
				return nil, errors.New("channel code not found")
			}

			productMap, err = filterAndFillPriceInfo(productMap, priceInfoMap, priceTypeMap, priceTypeID)
			if err != nil {
				return nil, errors.Join(errors.New("filter and fill price info error: "), err)
			}
			if productMap == nil {
				continue
			}
			filterProductList = append(filterProductList, productMap)
		}
		category["productList"] = filterProductList
		if len(filterProductList) > 0 {
			filterProductListArray = append(filterProductListArray, category)
		}
	}

	return json.Marshal(filterProductListArray)
}

func getProductInfoMap(productInfo []byte) (map[string]map[string]interface{}, error) {
	var productInfoArray []map[string]interface{}

	decoder := json.NewDecoder(bytes.NewReader(productInfo))
	decoder.UseNumber()

	if err := decoder.Decode(&productInfoArray); err != nil {
		return nil, errors.New("unmarshal product info error: " + err.Error())
	}

	productInfoMap := make(map[string]map[string]interface{})
	for _, info := range productInfoArray {
		productID, ok := info["product_id"].(string)
		if !ok {
			continue
		}
		productInfoMap[productID] = info
	}

	return productInfoMap, nil
}

func getProductAttr(productAttr []byte) (map[string]map[string]interface{}, error) {
	var productAttrArray []map[string]interface{}
	decoder := json.NewDecoder(bytes.NewReader(productAttr))
	decoder.UseNumber()

	if err := decoder.Decode(&productAttrArray); err != nil {
		return nil, errors.New("unmarshal product info error: " + err.Error())
	}

	productAttrMap := make(map[string]map[string]interface{})
	for _, attr := range productAttrArray {
		attrID, ok := attr["id"].(string)
		if !ok {
			continue
		}
		productAttrMap[attrID] = attr
	}

	return productAttrMap, nil
}

func getProductAttrValues(productAttrValues []byte) (map[string]map[string]interface{}, error) {
	var productAttrValuesArray []map[string]interface{}
	decoder := json.NewDecoder(bytes.NewReader(productAttrValues))
	decoder.UseNumber()

	if err := decoder.Decode(&productAttrValuesArray); err != nil {
		return nil, errors.New("unmarshal product info error: " + err.Error())
	}

	productAttrValuesMap := make(map[string]map[string]interface{})
	for _, attrValues := range productAttrValuesArray {
		attrValuesArray, ok := attrValues["attribute_values"].([]interface{})
		if !ok {
			continue
		}
		for _, attrValue := range attrValuesArray {
			attrValueMap, ok := attrValue.(map[string]interface{})
			if !ok {
				continue
			}
			attrValueID, ok := attrValueMap["id"].(string)
			if !ok {
				continue
			}
			productAttrValuesMap[attrValueID] = attrValueMap
		}
	}

	return productAttrValuesMap, nil
}

func setProductInfo(product map[string]interface{}, productInfoMap map[string]map[string]interface{}) error {
	productID, ok := product["product_id"].(string)
	if !ok {
		return errors.New("product_id is not a string")
	}

	info, ok := productInfoMap[productID]
	if !ok {
		return errors.New("product info not found")
	}

	if relation, ok := product["relation"].(map[string]interface{}); ok {
		if infoRelation, ok := info["relation"].(map[string]interface{}); ok {

			info["relation"] = mergeMaps(relation, infoRelation)
		}
	}

	product = mergeMaps(product, info)

	return nil
}

func setAttrValues(product map[string]interface{}, productAttrValuesMap map[string]map[string]interface{}) error {
	relation, ok := product["relation"].(map[string]interface{})
	if !ok {
		return errors.New("relation is not a map")
	}

	additionalAttribute, ok := relation["additional_attribute"].([]interface{})
	if !ok || additionalAttribute == nil {
		return nil
	}

	for i, attr := range additionalAttribute {
		attrMap, ok := attr.(map[string]interface{})
		if !ok {
			continue
		}

		attrValues, ok := attrMap["attribute_values"].([]interface{})
		if !ok {
			continue
		}

		for j, attrValue := range attrValues {
			attrValueMap, ok := attrValue.(map[string]interface{})
			if !ok {
				continue
			}

			attrValueID, ok := attrValueMap["id"].(string)
			if !ok {
				continue
			}

			attrValueInfo, ok := productAttrValuesMap[attrValueID]
			if !ok {
				continue
			}

			attrValueMap = mergeMaps(attrValueMap, attrValueInfo)

			attrValues[j] = attrValueMap
		}

		attrMap["attribute_values"] = attrValues
		additionalAttribute[i] = attrMap
	}

	relation["additional_attribute"] = additionalAttribute
	product["relation"] = relation

	return nil
}

// setSubItemSpu 补充subItemSpu
// 1. 取出关系中的set-->subItemSpu-->relation-->additional_attribute, 之后补充子商品属性
// 2. productList-->>relation-->>set-->>spu_id
// (根据商品spu_id,在商品列表接口中"product_id"找到对应商品,补充商品信息),
// 放在set中新增字段:"subItemSpu":{},把商品信息放入其中.
// 3. productList-->>relation-->>set-->>raisePrice 赋值给: subItemSpu-->>relation-->>sku-->>selling_price;
// 4. productList-->>relation-->>set-->>subItemSpu-->>relation-->>sku-->>sellingPrice 赋值给: subItemSpu-->>relation-->>sku
// -->>original_price
func setSubItemSpu(product map[string]interface{},
	productInfoMap map[string]map[string]interface{},
	productAttrMap,
	productAttrValuesMap map[string]map[string]interface{}) error {
	relation, ok := product["relation"].(map[string]interface{})
	if !ok {
		return errors.New("relation is not a map")
	}

	setArray, ok := relation["set"].([]interface{})
	if !ok || setArray == nil {
		return nil
	}

	for i, setItem := range setArray {
		setMap, ok := setItem.(map[string]interface{})
		if !ok {
			continue
		}

		spuID, ok := setMap["spu_id"].(string)
		if !ok {
			continue
		}

		spuInfo, ok := productInfoMap[spuID]
		if !ok {
			continue
		}

		subItemSpu := make(map[string]interface{})
		for k, v := range spuInfo {
			subItemSpu[k] = v
		}

		// Set raisePrice to subItemSpu->relation->sku->selling_price
		raisePrice, ok := setMap["raisePrice"].(json.Number)
		if ok {
			if relation, ok := subItemSpu["relation"].(map[string]interface{}); ok {
				if skuArray, ok := relation["sku"].([]interface{}); ok {
					for j, skuItem := range skuArray {
						if skuMap, ok := skuItem.(map[string]interface{}); ok {
							skuMap["selling_price"] = raisePrice
							skuArray[j] = skuMap
						}
					}
					relation["sku"] = skuArray
				}
			}
		}

		// Set subItemSpu->relation->sku->sellingPrice to subItemSpu->relation->sku->original_price
		if relation, ok := subItemSpu["relation"].(map[string]interface{}); ok {
			if skuArray, ok := relation["sku"].([]interface{}); ok {
				for j, skuItem := range skuArray {
					if skuMap, ok := skuItem.(map[string]interface{}); ok {
						if skuMap["selling_price"] != nil {
							skuMap["original_price"] = skuMap["selling_price"]
							skuArray[j] = skuMap
						}
					}
				}
				relation["sku"] = skuArray
			}
		}

		// set-->subItemSpu-->relation-->additional_attribute 补充属性
		if sub, ok := setMap["subItemSpu"].(map[string]interface{}); ok {
			if r, ok := sub["relation"].(map[string]interface{}); ok {
				if subRelation, ok := subItemSpu["relation"].(map[string]interface{}); ok {
					subRelation["additional_attribute"] = r["additional_attribute"]

					err := setProductAttr(subItemSpu, productAttrMap)
					if err != nil {
						return errors.Join(errors.New("set product attr error: "), err)
					}

					// 补充attribute_values
					if err := setAttrValues(subItemSpu, productAttrValuesMap); err != nil {
						return errors.Join(errors.New("set attr values error: "), err)
					}
				}
			}
		}

		setMap["subItemSpu"] = subItemSpu
		setArray[i] = setMap
	}

	relation["set"] = setArray
	product["relation"] = relation

	return nil
}

func setRelationAddition(product map[string]interface{}, productInfoMap map[string]map[string]interface{}) error {
	relation, ok := product["relation"].(map[string]interface{})
	if !ok {
		return errors.New("relation is not a map")
	}

	setArray, ok := relation["set"].([]interface{})
	if !ok || setArray == nil {
		return nil
	}

	for i, setItem := range setArray {
		setMap, ok := setItem.(map[string]interface{})
		if !ok {
			continue
		}

		addition, ok := setMap["addition"]
		if !ok {
			continue
		}

		//productID, ok := setMap["product_id"].(string)
		//if !ok {
		//	continue
		//}

		subItemSpu, ok := setMap["subItemSpu"].(map[string]interface{})
		if !ok {
			continue
		}

		//skuArray, ok := subItemSpu["relation"].(map[string]interface{})["sku"].([]interface{})
		//if !ok {
		//	continue
		//}

		//for j, skuItem := range skuArray {
		//	skuMap, ok := skuItem.(map[string]interface{})
		//	if !ok {
		//		continue
		//	}
		//
		//	if skuMap["id"] == productID {
		//		skuMap["addition"] = addition
		//		skuArray[j] = skuMap
		//	}
		//}

		subItemSpu["relation"].(map[string]interface{})["addition"] = addition
		setMap["subItemSpu"] = subItemSpu
		setArray[i] = setMap

		delete(setMap, "addition")
	}

	relation["set"] = setArray
	product["relation"] = relation

	return nil
}

func setProductAttr(product map[string]interface{}, productAttrMap map[string]map[string]interface{}) error {
	relation, ok := product["relation"].(map[string]interface{})
	if !ok {
		return errors.New("relation is not a map")
	}

	additionalAttribute, ok := relation["additional_attribute"]
	if !ok || additionalAttribute == nil {
		return nil
	}

	additionalAttributeArray, ok := additionalAttribute.([]interface{})
	if !ok || additionalAttributeArray == nil {
		return nil
	}

	for i, attr := range additionalAttributeArray {
		attrMap, ok := attr.(map[string]interface{})
		if !ok {
			continue
		}

		attrID, ok := attrMap["id"].(string)
		if !ok {
			continue
		}

		attrInfo, ok := productAttrMap[attrID]
		if !ok {
			continue
		}

		attrInfo = mergeMaps(attrMap, attrInfo)

		for k, v := range attrInfo {
			attrMap[k] = v
		}

		additionalAttributeArray[i] = attrMap
	}

	relation["additional_attribute"] = additionalAttributeArray
	product["relation"] = relation

	return nil
}

func mergeMaps(map1, map2 map[string]interface{}) map[string]interface{} {
	for k, v := range map2 {
		if map1[k] == nil || map1[k] == "" {
			map1[k] = v
		}
	}
	return map1
}

func getPriceInfoMap(priceInfo []byte) (map[string]map[string]interface{}, error) {
	var PriceInfoArray []map[string]interface{}
	priceInfo, _, _, err := jsonparser.Get(priceInfo, "rows")
	if err != nil {
		return nil, errors.New("get rows error: " + err.Error())
	}
	decoder := json.NewDecoder(bytes.NewReader(priceInfo))
	decoder.UseNumber()

	if err := decoder.Decode(&PriceInfoArray); err != nil {
		return nil, errors.New("unmarshal price info error: " + err.Error())
	}
	priceMap := make(map[string]map[string]interface{})

	for _, info := range PriceInfoArray {
		isValid, ok := info["is_valid"].(bool)
		if !ok {
			continue
		}
		if isValid == false {
			continue
		}
		itemID, ok := info["item_code"].(string)
		if !ok {
			continue
		}
		priceTypeID, ok := info["price_type_id"].(string)
		if !ok {
			continue
		}
		if priceTypePriceMap, ok := priceMap[itemID]; ok {
			priceTypePriceMap[priceTypeID] = info
		} else {
			priceMap[itemID] = map[string]interface{}{priceTypeID: info}
		}
	}
	return priceMap, nil
}

//	{
//	   "code": "UPA1",
//	   "name": "UPA1",
//	   "channel_code_map": "POS",
//	   "channel_code_list": [
//	       "POS",
//	       "POS_H5",
//	       "POS_APP"
//	   ]
//	}
//
// 返回 priceTypeMap: code->id channelMap: channel_code->id
func getPriceTypeMapAndChannelMap(priceTypeInfo []byte) (map[string]string, map[string]string, error) {
	var priceTypeInfoArray []map[string]interface{}
	decoder := json.NewDecoder(bytes.NewReader(priceTypeInfo))
	decoder.UseNumber()

	if err := decoder.Decode(&priceTypeInfoArray); err != nil {
		return nil, nil, errors.New("unmarshal price type info error: " + err.Error())
	}

	priceTypeMap := make(map[string]string)
	channelMap := make(map[string]string)
	for _, info := range priceTypeInfoArray {
		fields, ok := info["fields"].(map[string]interface{})
		if !ok {
			continue
		}
		id, ok := info["id"].(string)
		if !ok {
			continue
		}
		code, ok := fields["code"].(string)
		if !ok {
			continue
		}
		priceTypeMap[id] = code
		channelCodeList, ok := fields["channel_code_list"].([]interface{})
		if !ok {
			continue
		}
		for _, channelCode := range channelCodeList {
			channelCodeStr, ok := channelCode.(string)
			if !ok {
				continue
			}
			channelMap[channelCodeStr] = id
		}
	}
	return priceTypeMap, channelMap, nil
}
